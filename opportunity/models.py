from django.db import models

from core.enums import VietnamCityEnum
from core.model import BaseModel
from opportunity.enums import ProjectTypeEnum
from staff.models import Staff


# 商机模型
class Opportunity(BaseModel):
    # 项目编号
    number = models.CharField(max_length=255, verbose_name='项目编号', blank=True, default='')
    # 录入人
    input_user = models.ForeignKey(Staff, on_delete=models.SET_NULL, null=True, verbose_name='录入人')
    # 项目名称
    name = models.CharField(max_length=255, verbose_name='项目名称', blank=True, default='')
    # 项目类型
    type = models.CharField(max_length=255, verbose_name='项目类型', choices=ProjectTypeEnum.choices)
    # 预计签约日期
    expected_signing_date = models.DateField(verbose_name='预计签约日期')
    # 项目所在地
    location = models.CharField(max_length=255, verbose_name='项目所在地', choices=VietnamCityEnum.choices)
    # 项目地址
    address = models.CharField(max_length=255, verbose_name='项目地址', blank=True, default='')