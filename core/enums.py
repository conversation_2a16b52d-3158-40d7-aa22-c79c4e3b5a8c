from django.db import models

# 越南所有城市的枚举
class VietnamCityEnum(models.TextChoices):
    # 直辖市
    HO_CHI_MINH = 'HO_CHI_MINH', '胡志明市 (<PERSON><PERSON>)'
    HA_NOI = 'HA_NOI', '河内市 (<PERSON><PERSON>)'
    HAI_PHONG = 'HAI_PHONG', '海防市 (<PERSON><PERSON><PERSON>)'
    DA_NANG = 'DA_NANG', '岘港市 (Đà Nẵng)'
    CAN_THO = 'CAN_THO', '芹苴市 (<PERSON><PERSON><PERSON>)'

    # 北部省份
    HA_GIANG = 'HA_GIANG', '河江省 (Hà Giang)'
    CAO_BANG = 'CAO_BANG', '高平省 (Cao Bằng)'
    BAC_KAN = 'BAC_KAN', '北件省 (Bắc Kạn)'
    TUYEN_QUANG = 'TUYEN_QUANG', '宣光省 (<PERSON><PERSON><PERSON><PERSON>)'
    LAO_CAI = 'LAO_CAI', '老街省 (<PERSON><PERSON><PERSON>)'
    DIEN_BIEN = 'DIEN_BIEN', '奠边省 (Điệ<PERSON> Biên)'
    LAI_CHAU = 'LAI_CHAU', '莱州省 (Lai Châu)'
    SON_LA = 'SON_LA', '山罗省 (Sơn La)'
    YEN_BAI = 'YEN_BAI', '安沛省 (Yên Bái)'
    HOA_BINH = 'HOA_BINH', '和平省 (Hòa Bình)'
    THAI_NGUYEN = 'THAI_NGUYEN', '太原省 (Thái Nguyên)'
    LANG_SON = 'LANG_SON', '谅山省 (Lạng Sơn)'
    QUANG_NINH = 'QUANG_NINH', '广宁省 (Quảng Ninh)'
    BAC_GIANG = 'BAC_GIANG', '北江省 (Bắc Giang)'
    PHU_THO = 'PHU_THO', '富寿省 (Phú Thọ)'
    VINH_PHUC = 'VINH_PHUC', '永福省 (Vĩnh Phúc)'
    BAC_NINH = 'BAC_NINH', '北宁省 (Bắc Ninh)'
    HAI_DUONG = 'HAI_DUONG', '海阳省 (Hải Dương)'
    HUNG_YEN = 'HUNG_YEN', '兴安省 (Hưng Yên)'
    THAI_BINH = 'THAI_BINH', '太平省 (Thái Bình)'
    HA_NAM = 'HA_NAM', '河南省 (Hà Nam)'
    NAM_DINH = 'NAM_DINH', '南定省 (Nam Định)'
    NINH_BINH = 'NINH_BINH', '宁平省 (Ninh Bình)'

    # 中部省份
    THANH_HOA = 'THANH_HOA', '清化省 (Thanh Hóa)'
    NGHE_AN = 'NGHE_AN', '乂安省 (Nghệ An)'
    HA_TINH = 'HA_TINH', '河静省 (Hà Tĩnh)'
    QUANG_BINH = 'QUANG_BINH', '广平省 (Quảng Bình)'
    QUANG_TRI = 'QUANG_TRI', '广治省 (Quảng Trị)'
    THUA_THIEN_HUE = 'THUA_THIEN_HUE', '承天顺化省 (Thừa Thiên Huế)'
    QUANG_NAM = 'QUANG_NAM', '广南省 (Quảng Nam)'
    QUANG_NGAI = 'QUANG_NGAI', '广义省 (Quảng Ngãi)'
    BINH_DINH = 'BINH_DINH', '平定省 (Bình Định)'
    PHU_YEN = 'PHU_YEN', '富安省 (Phú Yên)'
    KHANH_HOA = 'KHANH_HOA', '庆和省 (Khánh Hòa)'
    NINH_THUAN = 'NINH_THUAN', '宁顺省 (Ninh Thuận)'
    BINH_THUAN = 'BINH_THUAN', '平顺省 (Bình Thuận)'
    KON_TUM = 'KON_TUM', '昆嵩省 (Kon Tum)'
    GIA_LAI = 'GIA_LAI', '嘉莱省 (Gia Lai)'
    DAK_LAK = 'DAK_LAK', '得乐省 (Đắk Lắk)'
    DAK_NONG = 'DAK_NONG', '得农省 (Đắk Nông)'
    LAM_DONG = 'LAM_DONG', '林同省 (Lâm Đồng)'

    # 南部省份
    BINH_PHUOC = 'BINH_PHUOC', '平福省 (Bình Phước)'
    TAY_NINH = 'TAY_NINH', '西宁省 (Tây Ninh)'
    BINH_DUONG = 'BINH_DUONG', '平阳省 (Bình Dương)'
    DONG_NAI = 'DONG_NAI', '同奈省 (Đồng Nai)'
    BA_RIA_VUNG_TAU = 'BA_RIA_VUNG_TAU', '头顿-巴地省 (Bà Rịa - Vũng Tàu)'
    LONG_AN = 'LONG_AN', '隆安省 (Long An)'
    TIEN_GIANG = 'TIEN_GIANG', '前江省 (Tiền Giang)'
    BEN_TRE = 'BEN_TRE', '槟椥省 (Bến Tre)'
    TRA_VINH = 'TRA_VINH', '茶荣省 (Trà Vinh)'
    VINH_LONG = 'VINH_LONG', '永隆省 (Vĩnh Long)'
    DONG_THAP = 'DONG_THAP', '同塔省 (Đồng Tháp)'
    AN_GIANG = 'AN_GIANG', '安江省 (An Giang)'
    KIEN_GIANG = 'KIEN_GIANG', '坚江省 (Kiên Giang)'
    HAU_GIANG = 'HAU_GIANG', '后江省 (Hậu Giang)'
    SOC_TRANG = 'SOC_TRANG', '朔庄省 (Sóc Trăng)'
    BAC_LIEU = 'BAC_LIEU', '薄辽省 (Bạc Liêu)'
    CA_MAU = 'CA_MAU', '金瓯省 (Cà Mau)'
    
    
    
# 